import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";
import dotenv from "dotenv";
import path from "path";

if (process.env.NODE_ENV !== "production") {
  dotenv.config({ path: path.resolve(__dirname, "../.env") });
}

// Function to get the current stage (default to 'dev')
export const getCurrentStage = (): string => {
  return process.env.APP_STAGE || "dev";
};

// Function to get config from Parameter Store or environment
const getConfigValue = async (
  paramName: string,
  envKey: string,
  defaultValue: string = "",
) => {
  if (process.env.USE_LOCAL_ENV_CONFIG === "true") {
    return process.env[envKey] || defaultValue;
  }

  const stage = getCurrentStage();
  const paramPath = `/hospice-os/${stage}/${paramName}`;
  try {
    let ssmClient: SSMClient;
    // Try to get from Parameter Store if AWS is configured
    if (process.env.AWS_REGION && process.env.NODE_ENV !== "development") {
      ssmClient = new SSMClient({
        region: process.env.AWS_REGION,
      });
    } else if (
      process.env.AWS_ACCESS_KEY_ID &&
      process.env.AWS_SECRET_ACCESS_KEY
    ) {
      ssmClient = new SSMClient({
        region: process.env.AWS_REGION,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
        },
      });
    }

    const paramResponse = await ssmClient.send(
      new GetParameterCommand({
        Name: paramPath,
        WithDecryption: true,
      }),
    );

    if (paramResponse.Parameter?.Value) {
      return paramResponse.Parameter.Value;
    }
  } catch (error) {
    console.log(
      `Failed to retrieve parameter ${paramPath}, falling back to env var:`,
      error,
    );
  }
  // Fall back to environment variable
  return process.env[envKey] || defaultValue;
};

// Load initial config from environment
export const config = {
  useInMemoryRepositories: process.env.USE_IN_MEMORY_REPOSITORIES || "false",
  port: process.env.PORT || 3000,
  cors: {
    allowedOrigins: process.env.CORS_ALLOWED_ORIGINS?.split(","),
  },
  stytch: {
    projectId: process.env.STYTCH_PROJECT_ID || "",
    secret: process.env.STYTCH_SECRET || "",
    env: process.env.STYTCH_ENV || "",
  },
  aws: {
    region: process.env.AWS_REGION || "us-east-1",
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
    s3Bucket: process.env.AWS_UPLOAD_BUCKET_NAME || "",
  },
  db: {
    host: process.env.DB_HOST || "",
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "",
    user: process.env.DB_USER || "",
    password: process.env.DB_PASSWORD || "",
    max: 20, // Maximum number of clients in the pool
    idleTimeoutMillis: 30000,
    ssl:
      process.env.NODE_ENV === "production" && getCurrentStage() !== "staging"
        ? true
        : false,
  },
};

// Function to initialize config with parameters if available
export const initializeConfig = async () => {
  // Update stytch config
  config.stytch.projectId = await getConfigValue(
    "STYTCH_PROJECT_ID",
    "STYTCH_PROJECT_ID",
    config.stytch.projectId,
  );
  config.stytch.secret = await getConfigValue(
    "STYTCH_SECRET",
    "STYTCH_SECRET",
    config.stytch.secret,
  );
  config.stytch.env = await getConfigValue(
    "STYTCH_ENV",
    "STYTCH_ENV",
    config.stytch.env,
  );

  // Update AWS config
  // config.aws.region = await getConfigValue('AWS_REGION', 'AWS_REGION', config.aws.region);
  // config.aws.accessKeyId = await getConfigValue('AWS_ACCESS_KEY_ID', 'AWS_ACCESS_KEY_ID', config.aws.accessKeyId);
  // config.aws.secretAccessKey = await getConfigValue('AWS_SECRET_ACCESS_KEY', 'AWS_SECRET_ACCESS_KEY', config.aws.secretAccessKey);
  config.aws.s3Bucket = await getConfigValue(
    "AWS_UPLOAD_BUCKET_NAME",
    "AWS_UPLOAD_BUCKET_NAME",
    config.aws.s3Bucket,
  );
  config.cors.allowedOrigins = (
    await getConfigValue(
      "CORS_ALLOWED_ORIGINS",
      "CORS_ALLOWED_ORIGINS",
      config.cors.allowedOrigins?.join(","),
    )
  )?.split(",");
  // Update DB config
  config.db.host = await getConfigValue("DB_HOST", "DB_HOST", config.db.host);
  config.db.port = parseInt(
    await getConfigValue("DB_PORT", "DB_PORT", config.db.port.toString()),
  );
  config.db.database = await getConfigValue(
    "DB_NAME",
    "DB_NAME",
    config.db.database,
  );
  config.db.user = await getConfigValue("DB_USER", "DB_USER", config.db.user);
  config.db.password = await getConfigValue(
    "DB_PASSWORD",
    "DB_PASSWORD",
    config.db.password,
  );
};
