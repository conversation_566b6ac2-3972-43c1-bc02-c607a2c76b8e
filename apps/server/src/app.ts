import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import { config } from "./config";
import apiRoutes from "./api";
import adminRoutes from "./api/admin";
import { initializeResourceEventSystem } from "./services/resource-events";
import { initializeServer } from "./init";
import { Server } from "http";

// Load environment variables from .env file

const run = async () => {
  await initializeServer({ migrate: true, configureStytch: true });

  // Create Express app
  const app = express();
  const port = config.port;

  // Log CORS configuration for debugging
  console.log("CORS Configuration:", {
    NODE_ENV: process.env.NODE_ENV,
    APP_STAGE: process.env.APP_STAGE,
    allowedOrigins: config.cors.allowedOrigins,
    allowedOriginsLength: config.cors.allowedOrigins?.length,
  });

  // Middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(
    cors({
      origin: function (origin, callback) {
        console.log(
          `CORS: Checking origin: ${origin}, NODE_ENV: ${process.env.NODE_ENV}`,
        );

        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) {
          console.log("CORS: Allowing request with no origin");
          return callback(null, true);
        }

        // In development, allow all origins
        if (process.env.NODE_ENV === "development") {
          console.log("CORS: Development mode - allowing all origins");
          return callback(null, true);
        }

        // In production/staging, check against allowed origins
        if (
          config.cors.allowedOrigins &&
          config.cors.allowedOrigins.length > 0
        ) {
          console.log(
            `CORS: Checking against allowed origins:`,
            config.cors.allowedOrigins,
          );
          if (config.cors.allowedOrigins.indexOf(origin) !== -1) {
            console.log(`CORS: Origin ${origin} is allowed`);
            callback(null, true);
          } else {
            console.warn(
              `CORS: Origin ${origin} not allowed. Allowed origins:`,
              config.cors.allowedOrigins,
            );
            callback(new Error("Not allowed by CORS"));
          }
        } else {
          // Fallback: if no allowed origins configured, deny all
          console.warn(
            "CORS: No allowed origins configured, denying request from:",
            origin,
          );
          callback(new Error("Not allowed by CORS"));
        }
      },
      credentials: true,
    }),
  );
  app.use(cookieParser());

  // API routes
  app.use("/api/admin", adminRoutes); // Mount admin routes first
  app.use("/api", apiRoutes);

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.json({ status: "ok" });
  });

  // Initialize resource event system
  initializeResourceEventSystem();

  // Start Express server and store reference
  const server: Server = app.listen(port, () => {
    console.log(`Server listening on port ${port}`);
  });

  // Handle server errors
  server.on("error", (error: any) => {
    if (error.code === "EADDRINUSE") {
      console.log(`Port ${port} is already in use. Retrying in 1 second...`);
      setTimeout(() => {
        server.close();
        server.listen(port);
      }, 1000);
    } else {
      console.error("Server error:", error);
    }
  });

  // Graceful shutdown handling
  const gracefulShutdown = (signal: string) => {
    console.log(`\nReceived ${signal}. Gracefully shutting down...`);

    server.close((err) => {
      if (err) {
        console.error("Error during server shutdown:", err);
        process.exit(1);
      }

      console.log("Server closed successfully");
      process.exit(0);
    });

    // Force close server after 10 seconds
    setTimeout(() => {
      console.error(
        "Could not close connections in time, forcefully shutting down",
      );
      process.exit(1);
    }, 10000);
  };

  // Listen for termination signals
  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));

  // Handle uncaught exceptions
  process.on("uncaughtException", (error) => {
    console.error("Uncaught Exception:", error);
    gracefulShutdown("uncaughtException");
  });

  process.on("unhandledRejection", (reason, promise) => {
    console.error("Unhandled Rejection at:", promise, "reason:", reason);
    gracefulShutdown("unhandledRejection");
  });
};

run();
